<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghang.cash.mapper.ProjectInfoMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.jinghang.cash.pojo.project.ProjectInfo">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="project_code" property="projectCode" jdbcType="VARCHAR"/>
        <result column="project_name" property="projectName" jdbcType="VARCHAR"/>
        <result column="flow_channel" property="flowChannel" jdbcType="VARCHAR"/>
        <result column="guarantee_code" property="guaranteeCode" jdbcType="VARCHAR"/>
        <result column="capital_channel" property="capitalChannel" jdbcType="VARCHAR"/>
        <result column="project_type_code" property="projectTypeCode" jdbcType="VARCHAR"/>
        <result column="enabled" property="enabled" jdbcType="VARCHAR"/>
        <result column="start_date" property="startDate" jdbcType="DATE"/>
        <result column="end_date" property="endDate" jdbcType="DATE"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="revision" property="revision" jdbcType="INTEGER"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, project_code, project_name, flow_channel, guarantee_code, capital_channel,
        project_type_code, enabled, start_date, end_date, remark, revision,
        created_by, created_time, updated_by, updated_time
    </sql>

    <!-- 根据项目编码查询项目信息 -->
    <select id="selectByProjectCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM project_info
        WHERE project_code = #{projectCode,jdbcType=VARCHAR}
        AND enabled = 'ENABLE'
        LIMIT 1
    </select>

    <!-- 查询所有启用状态的项目信息 -->
    <select id="selectAllEnabledProjects" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM project_info
        WHERE enabled = 'ENABLE'
        ORDER BY created_time DESC
    </select>

</mapper>
