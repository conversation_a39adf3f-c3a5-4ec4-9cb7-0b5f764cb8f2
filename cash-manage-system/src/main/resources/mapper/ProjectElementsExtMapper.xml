<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghang.cash.mapper.ProjectElementsExtMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.jinghang.cash.pojo.project.ProjectElementsExt">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="VARCHAR"/>
        <result column="project_code" property="projectCode" jdbcType="VARCHAR"/>
        <result column="interest_days_basis" property="interestDaysBasis" jdbcType="VARCHAR"/>
        <result column="allow_cross_day_repay" property="allowCrossDayRepay" jdbcType="VARCHAR"/>
        <result column="risk_model_channel" property="riskModelChannel" jdbcType="VARCHAR"/>
        <result column="loan_payment_channel" property="loanPaymentChannel" jdbcType="VARCHAR"/>
        <result column="deduction_bind_card_channel" property="deductionBindCardChannel" jdbcType="VARCHAR"/>
        <result column="deduction_merchant_code" property="deductionMerchantCode" jdbcType="VARCHAR"/>
        <result column="sign_channel" property="signChannel" jdbcType="VARCHAR"/>
        <result column="overdue_sms_sender" property="overdueSmsSender" jdbcType="VARCHAR"/>
        <result column="sms_channel" property="smsChannel" jdbcType="VARCHAR"/>
        <result column="grace_period_type" property="gracePeriodType" jdbcType="VARCHAR"/>
        <result column="grace_period_days" property="gracePeriodDays" jdbcType="VARCHAR"/>
        <result column="holiday_postpone" property="holidayPostpone" jdbcType="VARCHAR"/>
        <result column="credit_query_party" property="creditQueryParty" jdbcType="VARCHAR"/>
        <result column="credit_report_sender" property="creditReportSender" jdbcType="VARCHAR"/>
        <result column="collection_party" property="collectionParty" jdbcType="VARCHAR"/>
        <result column="push_collection_data" property="pushCollectionData" jdbcType="VARCHAR"/>
        <result column="allow_collection_waiver" property="allowCollectionWaiver" jdbcType="VARCHAR"/>
        <result column="revision" property="revision" jdbcType="INTEGER"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, parent_id, project_code, interest_days_basis, allow_cross_day_repay, risk_model_channel,
        loan_payment_channel, deduction_bind_card_channel, deduction_merchant_code,
        sign_channel, overdue_sms_sender, sms_channel, grace_period_type, grace_period_days,
        holiday_postpone, credit_query_party, credit_report_sender, collection_party,
        push_collection_data, allow_collection_waiver, revision,
        created_by, created_time, updated_by, updated_time
    </sql>

    <!-- 根据项目编码查询项目要素扩展 -->
    <select id="selectByProjectCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM project_elements_ext
        WHERE project_code = #{projectCode,jdbcType=VARCHAR}
        LIMIT 1
    </select>
    
    <!-- 根据parentId查询项目要素扩展 -->
    <select id="selectByParentId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM project_elements_ext
        WHERE parent_id = #{parentId,jdbcType=VARCHAR}
        LIMIT 1
    </select>

</mapper>