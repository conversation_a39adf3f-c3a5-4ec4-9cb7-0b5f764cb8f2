package com.jinghang.cash.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.jinghang.cash.api.dto.ProjectElementsExtDto;
import com.jinghang.cash.api.dto.ProjectElementsDto;
import com.jinghang.cash.api.dto.ProjectInfoDto;
import com.jinghang.cash.enums.AbleStatus;
import com.jinghang.cash.mapper.ProjectElementsExtMapper;
import com.jinghang.cash.mapper.ProjectElementsMapper;
import com.jinghang.cash.mapper.ProjectInfoMapper;
import com.jinghang.cash.pojo.project.ProjectElements;
import com.jinghang.cash.pojo.project.ProjectElementsExt;
import com.jinghang.cash.pojo.project.ProjectInfo;
import com.jinghang.cash.modules.project.service.ProjectInfoService;
import com.jinghang.cash.utils.RedisUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 项目信息服务实现类
 *
 * @Author: Lior
 * @CreateTime: 2025/8/20 10:16
 */
@Service
@RequiredArgsConstructor
public class ProjectInfoServiceImpl implements ProjectInfoService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectInfoServiceImpl.class);

    @Autowired
    private ProjectInfoMapper projectInfoMapper;

    @Autowired
    private ProjectElementsMapper projectElementsMapper;

    @Autowired
    private ProjectElementsExtMapper projectElementsExtMapper;

    private final RedisUtils redisUtils;

    /**
     * 项目信息缓存key前缀
     */
    private static final String PROJECT_INFO_CACHE_PREFIX = "PROJECT_INFO_";

    /**
     * 默认缓存过期时间：24 * 7小时
     */
    private static final Duration CACHE_DURATION = Duration.ofHours(24 * 7);

    /**
     * 根据项目编码查询项目完整信息（带缓存）
     *
     * @param projectCode 项目编码
     * @return 项目完整信息dto
     */
    @Override
    public ProjectInfoDto queryProjectInfo(String projectCode) {
        if (StringUtils.isBlank(projectCode)) {
            logger.info("项目编码为空，无法查询项目信息");
            return null;
        }

        // 构建缓存key
        String cacheKey = PROJECT_INFO_CACHE_PREFIX + projectCode;

        try {
            // 1. 先从缓存中获取
            Object cachedData = redisUtils.get(cacheKey);
            if (cachedData instanceof ProjectInfoDto) {
                logger.info("从缓存中获取到项目信息，projectCode: {}", projectCode);
                return (ProjectInfoDto) cachedData;
            }

            // 2. 缓存中没有，从数据库查询
            ProjectInfoDto projectInfoDto = queryProjectInfoFromDatabase(projectCode);

            // 3. 查询结果放入缓存
            if (projectInfoDto != null) {
                Duration cacheDuration = calculateCacheDuration(projectInfoDto.getElements());
                redisUtils.set(cacheKey, projectInfoDto, cacheDuration.getSeconds(), TimeUnit.SECONDS);
                logger.info("项目信息已放入缓存，projectCode: {}, 缓存时长: {}秒", projectCode, cacheDuration.getSeconds());
            }

            return projectInfoDto;

        } catch (Exception e) {
            logger.error("查询项目完整信息异常，projectCode: {}", projectCode, e);
            throw new RuntimeException("查询项目信息失败", e);
        }
    }

    /**
     * 从数据库查询项目完整信息
     *
     * @param projectCode 项目编码
     * @return 项目完整信息dto
     */
    private ProjectInfoDto queryProjectInfoFromDatabase(String projectCode) {
        logger.info("从数据库查询项目完整信息，projectCode: {}", projectCode);

        // 1. 查询项目基本信息，只查询状态为启用的项目
        ProjectInfo projectInfo = projectInfoMapper.selectByProjectCode(projectCode);
        if (projectInfo == null || !AbleStatus.ENABLE.equals(projectInfo.getEnabled())) {
            logger.info("未找到启用状态的项目信息，projectCode: {}", projectCode);
            return null;
        }

        // 2. 转换为dto
        ProjectInfoDto projectInfoDto = new ProjectInfoDto();
        BeanUtils.copyProperties(projectInfo, projectInfoDto);
        projectInfoDto.setEnabled(projectInfo.getEnabled().name());

        // 3. 查询项目要素
        ProjectElements elements = queryProjectElements(projectCode);
        if (elements != null) {
            ProjectElementsDto elementsDto = new ProjectElementsDto();
            BeanUtil.copyProperties(elements, elementsDto);
            projectInfoDto.setElements(elementsDto);
            logger.info("查询到项目要素信息，projectCode: {}, 时效类型: {}", projectCode, elements.getProjectDurationType());
        } else {
            logger.info("未找到项目要素信息，projectCode: {}", projectCode);
        }

        // 4. 查询项目要素扩展
        ProjectElementsExt elementsExt = null;
        if (elements != null) {
            elementsExt = projectElementsExtMapper.selectByParentId(elements.getId());
        }
        if (elementsExt != null) {
            ProjectElementsExtDto elementsExtDto = new ProjectElementsExtDto();
            BeanUtils.copyProperties(elementsExt, elementsExtDto);
            projectInfoDto.setElementsExt(elementsExtDto);
            logger.info("查询到项目要素扩展信息，projectCode: {}, parentId: {}", projectCode, elements.getId());
        } else {
            logger.info("未找到项目要素扩展信息，projectCode: {}", projectCode);
        }

        logger.info("项目完整信息查询完成，projectCode: {}", projectCode);
        return projectInfoDto;
    }

    /**
     * 查询项目要素，优先查询临时配置，如果没有或已过期则查询长期配置
     *
     * @param projectCode 项目编码
     * @return 项目要素
     */
    private ProjectElements queryProjectElements(String projectCode) {
        LocalDateTime currentTime = LocalDateTime.now();

        // 1. 先查询有效的临时项目要素
        ProjectElements temporaryElements = projectElementsMapper.selectValidTemporaryElements(
                projectCode, AbleStatus.ENABLE, "TEMPORARY", currentTime);

        if (temporaryElements != null) {
            logger.info("查询到有效的临时项目要素，projectCode: {}, 有效期: {} - {}",
                    projectCode, temporaryElements.getTempStartTime(), temporaryElements.getTempEndTime());
            return temporaryElements;
        }

        // 2. 没有有效的临时配置，查询长期配置
        ProjectElements longtimeElements = projectElementsMapper.selectByProjectCodeAndEnabledAndProjectDurationType(
                projectCode, AbleStatus.ENABLE, "LONGTIME");

        if (longtimeElements != null) {
            logger.info("查询到长期项目要素，projectCode: {}", projectCode);
            return longtimeElements;
        }

        logger.info("未找到任何有效的项目要素配置，projectCode: {}", projectCode);
        return null;
    }

    /**
     * 计算缓存时长，如果是临时配置则使用临时配置的结束时间，否则使用默认时长
     *
     * @param elementsDto 项目要素dto
     * @return 缓存时长
     */
    private Duration calculateCacheDuration(ProjectElementsDto elementsDto) {
        if (elementsDto == null) {
            return CACHE_DURATION;
        }

        // 如果是临时配置且有结束时间，计算到结束时间的时长
        if ("TEMPORARY".equals(elementsDto.getProjectDurationType())
                && elementsDto.getTempEndTime() != null) {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime endTime = elementsDto.getTempEndTime();

            if (endTime.isAfter(now)) {
                return Duration.between(now, endTime);
            }
        }

        // 长期配置或临时配置已过期，使用默认缓存时长
        return CACHE_DURATION;
    }

    /**
     * 查询所有生效项目信息
     *
     * @return 所有生效项目信息列表
     */
    @Override
    public List<ProjectInfoDto> queryAllEnabledProjects() {
        logger.info("开始查询所有生效项目信息");

        try {
            // 1. 查询所有启用状态的项目基本信息
            List<ProjectInfo> projectInfoList = projectInfoMapper.selectAllEnabledProjects();

            if (projectInfoList == null || projectInfoList.isEmpty()) {
                logger.info("未找到任何启用状态的项目信息");
                return new ArrayList<>();
            }

            logger.info("查询到 {} 个启用状态的项目", projectInfoList.size());

            // 2. 转换为DTO并填充完整信息
            List<ProjectInfoDto> projectInfoDtoList = new ArrayList<>();
            for (ProjectInfo projectInfo : projectInfoList) {
                ProjectInfoDto projectInfoDto = buildProjectInfoDto(projectInfo);
                if (projectInfoDto != null) {
                    projectInfoDtoList.add(projectInfoDto);
                }
            }

            logger.info("成功构建 {} 个项目完整信息", projectInfoDtoList.size());
            return projectInfoDtoList;

        } catch (Exception e) {
            logger.error("查询所有生效项目信息异常", e);
            throw new RuntimeException("查询所有生效项目信息失败", e);
        }
    }

    /**
     * 构建项目完整信息DTO
     *
     * @param projectInfo 项目基本信息
     * @return 项目完整信息DTO
     */
    private ProjectInfoDto buildProjectInfoDto(ProjectInfo projectInfo) {
        try {
            // 1. 转换基本信息为DTO
            ProjectInfoDto projectInfoDto = new ProjectInfoDto();
            BeanUtils.copyProperties(projectInfo, projectInfoDto);
            projectInfoDto.setEnabled(projectInfo.getEnabled().name());

            // 2. 查询项目要素
            ProjectElements elements = queryProjectElements(projectInfo.getProjectCode());
            if (elements != null) {
                ProjectElementsDto elementsDto = new ProjectElementsDto();
                BeanUtil.copyProperties(elements, elementsDto);
                projectInfoDto.setElements(elementsDto);
                logger.debug("查询到项目要素信息，projectCode: {}, 时效类型: {}",
                    projectInfo.getProjectCode(), elements.getProjectDurationType());
            } else {
                logger.debug("未找到项目要素信息，projectCode: {}", projectInfo.getProjectCode());
            }

            // 3. 查询项目要素扩展
            ProjectElementsExt elementsExt = null;
            if (elements != null) {
                elementsExt = projectElementsExtMapper.selectByParentId(elements.getId());
            }
            if (elementsExt != null) {
                ProjectElementsExtDto elementsExtDto = new ProjectElementsExtDto();
                BeanUtils.copyProperties(elementsExt, elementsExtDto);
                projectInfoDto.setElementsExt(elementsExtDto);
                logger.debug("查询到项目要素扩展信息，projectCode: {}, parentId: {}",
                    projectInfo.getProjectCode(), elements.getId());
            } else {
                logger.debug("未找到项目要素扩展信息，projectCode: {}", projectInfo.getProjectCode());
            }

            return projectInfoDto;

        } catch (Exception e) {
            logger.error("构建项目完整信息异常，projectCode: {}", projectInfo.getProjectCode(), e);
            return null;
        }
    }

    /**
     * 清除项目信息缓存
     *
     * @param projectCode 项目编码
     */
    @Override
    public void clearProjectInfoCache(String projectCode) {
        if (StringUtils.isBlank(projectCode)) {
            logger.info("项目编码为空，无法清除缓存");
            return;
        }

        String cacheKey = PROJECT_INFO_CACHE_PREFIX + projectCode;
        try {
            redisUtils.del(cacheKey);
            logger.info("已清除项目信息缓存，projectCode: {}", projectCode);
        } catch (Exception e) {
            logger.error("清除项目信息缓存异常，projectCode: {}", projectCode, e);
        }
    }
}
