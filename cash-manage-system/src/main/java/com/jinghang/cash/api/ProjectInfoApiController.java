package com.jinghang.cash.api;

import com.jinghang.cash.annotation.AnonymousAccess;
import com.jinghang.cash.api.dto.ProjectInfoDto;
import com.jinghang.cash.modules.project.service.ProjectInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 项目信息ApiController
 *
 * @Author: Lior
 * @CreateTime: 2025/8/20 13:38
 */
@RestController
@RequestMapping("/api/projectInfo")
public class ProjectInfoApiController implements ProjectInfoApiService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectInfoApiController.class);

    @Autowired
    private ProjectInfoService projectInfoService;

    /**
     * 根据项目编码查询项目完整信息
     *
     * @param projectCode 项目编码
     * @return 项目完整信息
     */
    @AnonymousAccess
    @Override
    public ProjectInfoDto queryProjectInfo(@PathVariable("projectCode") String projectCode) {
        logger.info("接收到查询项目信息请求，projectCode: {}", projectCode);

        try {
            // 参数校验
            if (projectCode == null || projectCode.trim().isEmpty()) {
                logger.info("项目编码为空");
                return null;
            }

            // 调用业务服务
            ProjectInfoDto projectInfo = projectInfoService.queryProjectInfo(projectCode.trim());

            if (projectInfo == null) {
                logger.info("未找到项目信息，projectCode: {}", projectCode);
                return null;
            }

            logger.info("查询项目信息成功，projectCode: {}", projectCode);
            return projectInfo;

        } catch (Exception e) {
            logger.error("查询项目信息异常，projectCode: {}", projectCode, e);
            return null;
        }
    }

    /**
     * 查询所有生效项目信息
     *
     * @return 所有生效项目信息列表
     */
    @AnonymousAccess
    @Override
    public List<ProjectInfoDto> queryAllEnabledProjects() {
        logger.info("接收到查询所有生效项目信息请求");

        try {
            // 调用业务服务
            List<ProjectInfoDto> projectInfoList = projectInfoService.queryAllEnabledProjects();

            if (projectInfoList == null || projectInfoList.isEmpty()) {
                logger.info("未找到任何生效项目信息");
                return projectInfoList;
            }

            logger.info("查询所有生效项目信息成功，共 {} 个项目", projectInfoList.size());
            return projectInfoList;

        } catch (Exception e) {
            logger.error("查询所有生效项目信息异常", e);
            return null;
        }
    }
}
