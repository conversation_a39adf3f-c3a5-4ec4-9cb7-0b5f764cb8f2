package com.maguo.loan.cash.flow.service;


import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.config.RiskConfig;
import com.maguo.loan.cash.flow.dto.CallBackDTO;
import com.maguo.loan.cash.flow.entity.Credit;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.OrderEventRecord;
import com.maguo.loan.cash.flow.entity.OrderRouterRecord;
import com.maguo.loan.cash.flow.entity.PreLoanAuditRecord;
import com.maguo.loan.cash.flow.entity.UserInfo;
import com.maguo.loan.cash.flow.entity.UserRiskRecord;
import com.maguo.loan.cash.flow.entity.UserRiskRecordExternal;
import com.maguo.loan.cash.flow.entity.common.ProjectElements;
import com.maguo.loan.cash.flow.entity.common.ProjectElementsExt;
import com.maguo.loan.cash.flow.entity.vo.ProjectInfoVO;
import com.maguo.loan.cash.flow.entrance.common.service.ProjectInfoService;
import com.maguo.loan.cash.flow.enums.AuditState;
import com.maguo.loan.cash.flow.enums.CallbackState;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.OrderEvent;
import com.maguo.loan.cash.flow.enums.OrderState;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.RiskModelChannel;
import com.maguo.loan.cash.flow.enums.RouteState;
import com.maguo.loan.cash.flow.enums.WhetherState;
import com.maguo.loan.cash.flow.repository.CreditRepository;
import com.maguo.loan.cash.flow.repository.LoanRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OrderEventRecordRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.OrderRouterRecordRepository;
import com.maguo.loan.cash.flow.repository.PreLoanAuditRecordRepository;
import com.maguo.loan.cash.flow.repository.UserInfoRepository;
import com.maguo.loan.cash.flow.repository.UserRiskRecordRepository;
import com.maguo.loan.cash.flow.service.common.loan.LoanCommonCheckService;
import com.maguo.loan.cash.flow.service.common.loan.LoanCommonService;
import com.maguo.loan.cash.flow.service.event.CreditResultEvent;
import com.maguo.loan.cash.flow.util.RedisKeyConstants;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 订单服务
 */
@Service
public class OrderService {

    private static final Logger logger = LoggerFactory.getLogger(OrderService.class);

    /**
     * token失效时间
     * 6 hour
     */
    private static final long TOKEN_EXPIRE_TIME = 6;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private LoanRepository loanRepository;

    @Autowired
    private LoanRecordRepository loanRecordRepository;

    @Autowired
    private PreLoanAuditRecordRepository preLoanAuditRecordRepository;


    @Autowired
    private CacheService cacheService;

    @Autowired
    private WarningService warningService;

    @Autowired
    private MqService mqService;

    @Value("${flow.routing.suspended:}")
    private String flowSuspended;

    @Value("${flow.routing.day.loan:}")
    private boolean flowDayLoan;

    public static final int LOCK_WAIT_SECOND = 3;

    public static final int LOCK_RELEASE_SECOND = 8;

    @Autowired
    private LockService lockService;

    @Autowired
    private OrderEventRecordRepository orderEventRecordRepository;

    @Autowired
    private OrderRouterRecordRepository orderRouterRecordRepository;

    @Autowired
    private UserRiskRecordRepository userRiskRecordRepository;

    @Autowired
    private CreditRepository creditRepository;

    @Autowired
    private UserInfoRepository userInfoRepository;

    @Autowired
    private UserService userService;

    @Value("${pre.loan.audit.flowChannel}")
    private String preLoanAuditFlowChannel;
    @Value("${pre.loan.audit.flag}")
    private boolean preLoanAuditFlag;

    @Autowired
    private LoanCommonService loanCommonService;

    @Autowired
    private RiskConfig riskConfig;

    @Autowired
    private LoanCommonCheckService loanCommonCheckService;


    @Autowired
    private ProjectInfoService projectInfoService;
    @Autowired
    private CreditService creditService;


    private ApplicationEventPublisher eventPublisher;
    @Autowired
    public void setEventPublisher(ApplicationEventPublisher eventPublisher) {
        this.eventPublisher = eventPublisher;
    }

    public Order findById(String id) {
        return orderRepository.findById(id).orElseThrow();
    }


    /**
     * 下单
     */
    public void apply(String orderId, WhetherState rightsMarking) {
        Order order = orderRepository.findById(orderId).orElseThrow(() -> new BizException(ResultCode.ORDER_NOT_EXIST));
        if (OrderState.AUDIT_PASS != order.getOrderState()) {
            throw new BizException(ResultCode.NO_SUBMIT_REPEAT);
        }
        if (StringUtils.isBlank(order.getLoanCardId())) {
            throw new BizException(ResultCode.NO_BIND_CARD_SUBMIT_LOAN);
        }
        // 调用参数校验服务
        loanCommonCheckService.checkLoanParameters(order);
        //放款日限额校验
        boolean result = loanCommonService.limitDayLoanFlowChannel(order);
        if (result && flowDayLoan) {
            order.setOrderSubmitState(WhetherState.Y);
            order.setRightsMarking(rightsMarking);
            order.setOrderState(OrderState.LOAN_FAIL);
            order.setRemark("头寸不足");
            orderRepository.saveAndFlush(order);
            return;
        }

        //新增事件
        addOrderEvent(order, WhetherState.Y == rightsMarking ? OrderEvent.SUBMIT_WITH_RIGHTS : OrderEvent.SUBMIT_WITHOUT_RIGHTS);
        order.setOrderSubmitState(WhetherState.Y);
        order.setRightsMarking(rightsMarking);

        if (StringUtils.isEmpty(order.getProjectCode())){
            //没有项目编码，继续走路由
            routeApply(order);
        }else {
            ProjectInfoVO projectInfoVO = projectInfoService.queryProjectInfo(order.getProjectCode());
            ProjectElements elements = projectInfoVO.getElements();//项目要素
            ProjectElementsExt elementsExt = projectInfoVO.getElementsExt();//项目要素扩展

            //路由规则判断
            if ("路由".equals(elements.getCapitalRoute())){
                routeApply(order);
            }
            if ("直连".equals(elements.getCapitalRoute())){
                directApply(order,elementsExt);
            }
            logger.warn("支用下单，没有对应的路由方式，orderId：[{}]",orderId);
            warningService.warn("支用下单，没有对应的路由方式，orderId：[{}]",orderId);
        }
    }


    /**
     * 路由下单
     */
    public void routeApply(Order order) {
        if (Arrays.asList(riskConfig.getRiskLoanFlowChannel().split(",")).contains(order.getFlowChannel().name())) {
            //需要进行放款风控的流量渠道
            order.setOrderState(OrderState.CREDITING);
            order = orderRepository.saveAndFlush(order);
            UserRiskRecord userRiskRecord = getLoanUserRiskRecord(order);
            mqService.submitRiskLoanApply(userRiskRecord.getId());
        } else {
            order = orderRepository.saveAndFlush(order);
            orderRoute(order);
        }
        //其他渠道 风控通过的订单置为放款取消
        cancelOtherOrders(order);
    }

    /**
     * 直连下单
     */
    public void directApply(Order order,ProjectElementsExt elementsExt) {
        if (Arrays.asList(riskConfig.getRiskLoanFlowChannel().split(",")).contains(order.getFlowChannel().name())) {
            //需要进行放款风控的流量渠道
            //判断风控渠道
            if (RiskModelChannel.INTERNAL == elementsExt.getRiskModelChannel()){
                //内部风控
                order.setOrderState(OrderState.CREDITING);
                order = orderRepository.saveAndFlush(order);
                UserRiskRecord userRiskRecord = getLoanUserRiskRecord(order);
                mqService.submitRiskLoanApply(userRiskRecord.getId());
            }
            if (RiskModelChannel.BW == elementsExt.getRiskModelChannel()){
                //百维风控
                order.setOrderState(OrderState.CREDITING);
                order = orderRepository.saveAndFlush(order);
                UserRiskRecordExternal userRiskRecord = getBaiWeiLoanUserRiskRecord(order);
                mqService.submitBaiWeiRiskLoanApply(userRiskRecord.getId());
            }
        } else {
            order = orderRepository.saveAndFlush(order);
            //todo 直连授信
            creditService.directApply(order);
        }
        //其他渠道 风控通过的订单置为放款取消
        cancelOtherOrders(order);
    }

    /**
     * 下单
     */
    public void BaiWeiApply(String orderId, WhetherState rightsMarking) {
        Order order = orderRepository.findById(orderId).orElseThrow(() -> new BizException(ResultCode.ORDER_NOT_EXIST));
        if (OrderState.AUDIT_PASS != order.getOrderState()) {
            throw new BizException(ResultCode.NO_SUBMIT_REPEAT);
        }
        if (StringUtils.isBlank(order.getLoanCardId())) {
            throw new BizException(ResultCode.NO_BIND_CARD_SUBMIT_LOAN);
        }
        // 调用参数校验服务
        loanCommonCheckService.checkLoanParameters(order);
        //放款日限额校验
        boolean result = loanCommonService.limitDayLoanFlowChannel(order);
        if (result && flowDayLoan) {
            order.setOrderSubmitState(WhetherState.Y);
            order.setRightsMarking(rightsMarking);
            order.setOrderState(OrderState.LOAN_FAIL);
            order.setRemark("头寸不足");
            orderRepository.saveAndFlush(order);
            return;
        }

        //新增事件
        addOrderEvent(order, WhetherState.Y == rightsMarking ? OrderEvent.SUBMIT_WITH_RIGHTS : OrderEvent.SUBMIT_WITHOUT_RIGHTS);
        order.setOrderSubmitState(WhetherState.Y);
        order.setRightsMarking(rightsMarking);

        if (Arrays.asList(riskConfig.getFqlRiskLoanFlowChannel().split(",")).contains(order.getFlowChannel().name())) {
            //放款风控
            order.setOrderState(OrderState.CREDITING);
            order = orderRepository.saveAndFlush(order);
            UserRiskRecordExternal userRiskRecord = getBaiWeiLoanUserRiskRecord(order);
            if (FlowChannel.FQLQY001.equals(order.getFlowChannel())){
                mqService.submitBaiWeiRiskLoanApply(userRiskRecord.getId());
            }
        } else {
            order = orderRepository.saveAndFlush(order);
            orderRoute(order);
        }
        //其他渠道 风控通过的订单置为放款取消
        cancelOtherOrders(order);
    }


    /**
     * 资方授信结果,回到此方法处理后续
     *
     * @param creditId
     */
    public void loopBack(String creditId) {
        Credit credit = creditRepository.findById(creditId).orElseThrow(() -> new BizException(ResultCode.CREDIT_NOT_FOUND));
        Order order = orderRepository.findById(credit.getOrderId()).orElseThrow(() -> new BizException(ResultCode.ORDER_NOT_EXIST));
        switch (credit.getState()) {
            case FAILED -> {
                if (FlowChannel.FQLQY001.equals(order.getFlowChannel())){
                    //资方 授信失败 通知百维
                    mqService.submitBaiWeiRiskNotify(order.getRiskId());
                }
            }
            case SUCCEED -> afterCreditSucceed(order, credit);
            default -> {
            }
        }
    }

    /**
     * 资方授信成功后
     *
     * @param order
     */
    private void afterCreditSucceed(Order order, Credit credit) {
        // 订单状态
        logger.info("授信成功,路由结束:{}", order.getId());
        order.setBankChannel(credit.getBankChannel());
        order.setOrderState(OrderState.CREDITING.equals(order.getOrderState()) ? OrderState.CREDIT_PASS : order.getOrderState());
        orderRepository.save(order);

        // 授信成功事件
        eventPublisher.publishEvent(new CreditResultEvent(credit.getId(), order.getId()));
    }

    public void delayAutoLoan(String orderId) {
        Order order = orderRepository.findById(orderId).orElseThrow(() -> new BizException(ResultCode.ORDER_NOT_EXIST));
        if (OrderState.AUDIT_PASS != order.getOrderState()) {
            logger.error("订单状态已变更,订单id:{}", orderId);
            return;
        }
        orderRoute(order);
        //回调下单
        CallBackDTO callBackDTO = new CallBackDTO();
        callBackDTO.setFlowChannel(order.getFlowChannel());
        callBackDTO.setBusinessId(order.getId());
        callBackDTO.setCallbackState(CallbackState.ORDER_APPLY);
        mqService.submitCallbackCommonNotify(JsonUtil.toJsonString(callBackDTO));
        //其他渠道 风控通过的订单置为放款取消
        cancelOtherOrders(order);
    }

    public void cancelOtherOrders(Order order) {
        try {
            List<Order> cancelOrderList = orderRepository
                .findByUserIdAndOrderStateInAndIdNot(order.getUserId(), List.of(OrderState.AUDIT_PASS), order.getId());
            if (!CollectionUtils.isEmpty(cancelOrderList)) {
                cancelOrderList.stream()
                    .filter(o -> !o.getId().equals(order.getId()))
                    .forEach(o -> cancel(o.getId(), "orderApply"));
            }
        } catch (Exception e) {
            warningService.warn("放款申请，取消其他渠道订单失败, userId:" + order.getUserId(), msg -> logger.error("放款申请，取消其他渠道订单失败", e));
        }
    }


    /**
     * 订单路由
     *
     * @param order
     */
    public void orderRoute(Order order) {
        String orderId = order.getId();
        String flowName = order.getFlowChannel().name();
        logger.info("订单[{} - {}]开始授信路由", flowName, orderId);
        //挂起订单
        if (Arrays.asList(flowSuspended.split(",")).contains(flowName)) {
            order.setOrderState(OrderState.SUSPENDED);
            orderRepository.save(order);
        } else {
            if (!Arrays.asList(riskConfig.getRiskLoanFlowChannel().split(",")).contains(flowName)) {
                // 置授信中
                order.setOrderState(OrderState.CREDITING);
                orderRepository.save(order);
            }
            //如果是挂起激活
            if(OrderState.SUSPENDED.equals(order.getOrderState())){
                // 置授信中
                order.setOrderState(OrderState.CREDITING);
                orderRepository.save(order);
            }
            // 开始路由
            mqService.submitCreditRouteApply(orderId);
        }
    }

    /**
     * 查询用户订单
     *
     * @param outerOrderId 外部订单id
     * @param orderId      订单id
     * @param flowChannel  流量渠道
     * @return 订单
     */
    public Order query(String outerOrderId, String orderId, FlowChannel flowChannel) {
        if (!StringUtil.isEmpty(orderId)) {
            return orderRepository.findOrderById(orderId);
        }
        return orderRepository.findTopByOuterOrderIdAndFlowChannel(outerOrderId, flowChannel);
    }

    public Order update(Order order) {
        return orderRepository.save(order);
    }

    public List<Order> listByOuterUserId(String openId, FlowChannel flowChannel) {
        return orderRepository.findAllByOpenIdAndFlowChannel(openId, flowChannel);
    }

    public List<Order> listByOuterUserIdAndOrderState(String openId, OrderState orderState, FlowChannel flowChannel) {
        return orderRepository.findAllByOpenIdAndOrderStateAndFlowChannel(openId, orderState, flowChannel);
    }

    /**
     * 返回用户的所有订单
     *
     * @param userId      用户id
     * @param flowChannel 流量渠道
     * @return 订单列表
     */
    public List<Order> listByUserId(String userId, FlowChannel flowChannel) {
        return orderRepository.findAllByUserIdAndFlowChannel(userId, flowChannel);
    }

    public Order findByRiskId(String riskId) {
        return orderRepository.findByRiskId(riskId);
    }


    /**
     * 返回用户的所有订单
     *
     * @param userId      用户id
     * @param flowChannel 流量渠道
     * @return 订单列表
     */
    public List<Order> listByUserIdAndOrderState(String userId, OrderState orderState, FlowChannel flowChannel) {
        return orderRepository.findAllByUserIdAndOrderStateAndFlowChannel(userId, orderState, flowChannel);
    }


    public List<Order> listByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return orderRepository.findAllById(ids);
    }
    /**
     * 查询复借订单
     *
     */
    /**
     * 查询复借订单
     */
    public List<Order> repeatedBorrowing(String userId, List<FlowChannel> flowChannels) {
        return orderRepository.findAllByUserIdAndOrderStateAndFlowChannelIn(userId, OrderState.CLEAR, flowChannels);
    }

    public List<Order> listByOrderStateAndLoanTime(OrderState orderState, LocalDate reccDate, FlowChannel flowChannel) {
        LocalDateTime startDay = reccDate.atStartOfDay();
        LocalDateTime endDay = reccDate.plusDays(1L).atStartOfDay().minusSeconds(1);
        return orderRepository.findAllByOrderStateAndFlowChannelAndLoanTimeBetween(orderState, flowChannel, startDay, endDay);
    }

    /**
     * 返回流量复贷标识
     *
     * @return true:是复贷订单
     */
    public boolean isRepeatLoan(Order order, FlowChannel flowChannel) {
        LocalDateTime loanTime = order.getLoanTime();
        if (loanTime == null) {
            loanTime = LocalDateTime.now();
        }
        // 该用户放款成功的订单
        List<Loan> loanList = loanRepository.findByUserIdAndFlowChannelAndLoanState(order.getUserId(), flowChannel, ProcessState.SUCCEED);
        final LocalDateTime matchTime = loanTime;
        //是否复贷
        return loanList.stream().anyMatch(l -> l.getLoanTime().isBefore(matchTime));
    }

    public void closeSuspended(String orderId, String updateBy) {
        logger.info("取消挂起订单: orderId = {}, updateBy = {}", orderId, updateBy);

        // 获取订单
        Order order = orderRepository.findOrderById(orderId);
        // 获取锁
        Locker lock = lockService.getLock(RedisKeyConstants.ORDER_SUBMIT + order.getCertNo());
        boolean locked = false;
        try {
            locked = lock.tryLock(Duration.ZERO, Duration.ofSeconds(LOCK_RELEASE_SECOND));
            if (!locked) {
                logger.info("取消订单: orderId = {} ,未获取到锁", orderId);
                return;
            }

            // 查询最新订单状态
            order = orderRepository.findOrderById(orderId);
            if (order.getOrderState() != OrderState.SUSPENDED) {
                logger.info("当前订单:{}, 状态已变为{}, 不关闭该订单", orderId, order.getOrderState());
                return;
            }

            // 查询授信信息
            if (creditRepository.existsByOrderIdAndStateNotIn(orderId, ProcessState.FAILED, ProcessState.SUSPEND, ProcessState.SUCCEED)) {
                logger.info("当前订单:{}, 存在处理中的授信, 不关闭该订单", orderId);
                return;
            }

            //贷款前审核
            Optional<PreLoanAuditRecord> preLoanAuditRecordOptional = preLoanAuditRecordRepository.findByOrderId(orderId);
            if (preLoanAuditRecordOptional.isPresent()) {
                PreLoanAuditRecord preLoanAuditRecord = preLoanAuditRecordOptional.get();
                if (preLoanAuditRecord.getApproveResult() == AuditState.INIT || preLoanAuditRecord.getApproveResult() == AuditState.AUDITING) {
                    logger.info("当前订单:{}, 贷前审批存在处理中, 不关闭该订单", orderId);
                    return;
                }
            }

            // 查询贷款信息
            Loan loan = loanRepository.findByOrderId(orderId);
            if (loan != null) {
                if (loan.getLoanState() != ProcessState.PROCESSING && loan.getLoanState() != ProcessState.FAILED) {
                    logger.info("当前订单:{}, loan状态已变为{}, 不关闭该订单", orderId, loan.getLoanState());
                    return;
                }
                boolean hasNonFailedLoanRecord = loanRecordRepository.existsByLoanIdAndLoanStateNotIn(loan.getId(), ProcessState.FAILED);
                if (hasNonFailedLoanRecord) {
                    logger.info("当前订单:{}, 存在未失败的loanRecord，不关闭该订单", orderId);
                    return;
                }
            }
            // 取消贷款
            if (null != loan) {
                loan.setLoanState(ProcessState.FAILED);
                loan.setRemark("京银融挂起超过2天, 放款取消");
                loan.setUpdatedBy(updateBy);
                loanRepository.save(loan);
            }

            // 修改订单状态
            order.setOrderState(OrderState.LOAN_CANCEL);
            order.setUpdatedBy(updateBy);
            order.setRemark("京银融挂起超过2天, 放款取消");
            orderRepository.save(order);


        } catch (Exception e) {
            logger.error("京银融取消订单异常: orderId = {}", orderId, e);
        } finally {
            if (locked) {
                lock.unlock();
            }
        }
    }


    public void cancel(String orderId, String updateBy) {
        logger.info("取消订单: orderId = {}, updateBy = {}", orderId, updateBy);

        Order order = orderRepository.findOrderById(orderId);
        Locker lock = lockService.getLock(RedisKeyConstants.ORDER_SUBMIT + order.getCertNo());
        try {
            boolean locked = lock.tryLock(Duration.ZERO, Duration.ofSeconds(LOCK_RELEASE_SECOND));
            if (!locked) {
                logger.info("取消订单: orderId = {} ,未获取到锁", orderId);
                return;
            }
            //查询最新订单状态
            order = orderRepository.findOrderById(orderId);
            //查询订单状态
            OrderState orderState = order.getOrderState();
            if (OrderState.AUDIT_PASS != orderState && orderState != OrderState.SUSPENDED) {
                logger.info("当前订单:{},状态已变为{},不关闭该订单", orderId, orderState);
                return;
            }

            if (order.getOrderState() == OrderState.SUSPENDED) {
                // 存在处理中授信的话不取消
                boolean isExistsProcessingCredit = creditRepository.existsByOrderIdAndStateNotIn(orderId, ProcessState.FAILED, ProcessState.SUCCEED);
                if (isExistsProcessingCredit) {
                    logger.info("当前订单:{},存在审批中的资方授信记录", orderId);
                    return;
                }

                // 存在处理中要款的话不取消
                boolean loanRecords = loanRecordRepository.existsByOrderIdAndLoanStateNotIn(orderId, ProcessState.FAILED);
                if (loanRecords) {
                    logger.info("当前订单:{},存在审批中的资方申请要款记录", orderId);
                    return;
                }
                // 正在二次风控的话不取消
                Optional<PreLoanAuditRecord> optionalPreLoanAuditRecord = preLoanAuditRecordRepository.findByOrderId(orderId);
                if (optionalPreLoanAuditRecord.isPresent()) {
                    PreLoanAuditRecord preLoanAuditRecord = optionalPreLoanAuditRecord.get();
                    if (preLoanAuditRecord.getApproveResult() != AuditState.PASS && preLoanAuditRecord.getApproveResult() != AuditState.REJECT) {
                        logger.info("当前订单:{},风控二次审批中", orderId);
                        return;
                    }
                }
                Loan loan = loanRepository.findByOrderId(orderId);
                if (Objects.nonNull(loan)) {
                    loan.setLoanState(ProcessState.FAILED);
                    loanRepository.save(loan);
                }
            }
            //修改订单状态
            order.setOrderState(OrderState.LOAN_CANCEL);
            order.setUpdatedBy(updateBy);
            orderRepository.save(order);

            //取消订单路由
            cancelOrderRouter(orderId, "放款取消,取消本条路由");

            //放款取消
            CallBackDTO callBackDTO = new CallBackDTO();
            callBackDTO.setFlowChannel(order.getFlowChannel());
            callBackDTO.setBusinessId(order.getId());
            callBackDTO.setCallbackState(CallbackState.LOAN_CANCEL);
            mqService.submitCallbackCommonNotify(JsonUtil.toJsonString(callBackDTO));
        } catch (Exception e) {
            logger.error("取消订单异常: orderId = {}", orderId, e);
        } finally {
            lock.unlock();
        }
    }


    /**
     * 记录订单事件
     *
     * @param order      订单
     * @param orderEvent 事件
     */
    public void addOrderEvent(Order order, OrderEvent orderEvent) {
        OrderEventRecord orderEventRecord = new OrderEventRecord();
        orderEventRecord.setOrderId(order.getId());
        orderEventRecord.setFlowChannel(order.getFlowChannel());
        orderEventRecord.setUserId(order.getUserId());
        orderEventRecord.setOrderEvent(orderEvent);
        orderEventRecord.setEventTime(LocalDateTime.now());
        orderEventRecordRepository.save(orderEventRecord);
    }

    /**
     * 将所有等待的路由记录置为取消
     */
    private void cancelOrderRouter(String orderId, String cancelReason) {
        List<OrderRouterRecord> otherRecords = orderRouterRecordRepository.findByOrderIdAndRouteStateIn(orderId, RouteState.WAIT, RouteState.SUSPEND);
        otherRecords.forEach(r -> {
            r.setRouteFailReason(cancelReason);
            r.setRouteState(RouteState.CANCEL);
        });
        orderRouterRecordRepository.saveAll(otherRecords);
    }

    /**
     * 支用调用风控策略审批
     *
     * @param order
     * @return
     */
    public UserRiskRecord getLoanUserRiskRecord(Order order) {
        UserInfo userInfo = userInfoRepository.findByCertNo(order.getCertNo());
        return userService.createRiskLoanRecord(userInfo.getId(), order.getFlowChannel(), order.getApplyChannel(),order.getProjectCode());
    }

    /**
     * 支用调用风控策略审批
     *
     * @param order
     * @return
     */
    public UserRiskRecordExternal getBaiWeiLoanUserRiskRecord(Order order) {
        UserInfo userInfo = userInfoRepository.findByCertNo(order.getCertNo());
        return userService.createBaiWeiRiskLoanRecord(userInfo.getId(), order.getFlowChannel(), order.getApplyChannel());
    }


}
