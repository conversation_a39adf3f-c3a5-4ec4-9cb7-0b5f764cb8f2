package com.maguo.loan.cash.flow.convert;

import com.jinghang.cash.api.dto.ProjectElementsDto;
import com.jinghang.cash.api.dto.ProjectElementsExtDto;
import com.jinghang.cash.api.dto.ProjectInfoDto;
import com.maguo.loan.cash.flow.entity.common.ProjectElements;
import com.maguo.loan.cash.flow.entity.common.ProjectElementsExt;
import com.maguo.loan.cash.flow.entity.vo.ProjectInfoVO;
import com.maguo.loan.cash.flow.enums.AbleStatus;
import com.maguo.loan.cash.flow.enums.ProjectDurationType;
import com.maguo.loan.cash.flow.enums.RiskModelChannel;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;

/**
 * 项目信息转换器
 *
 * @Author: Lior
 * @CreateTime: 2025/8/22 15:30
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ProjectInfoConverter {

    /**
     * ProjectInfoDto转换为ProjectInfoVO
     */
    @Mapping(target = "enabled", source = "enabled", qualifiedByName = "stringToAbleStatus")
    ProjectInfoVO toProjectInfoVO(ProjectInfoDto dto);

    /**
     * ProjectElementsDto转换为ProjectElements
     */
    @Mapping(target = "enabled", source = "enabled", qualifiedByName = "stringToAbleStatus")
    @Mapping(target = "projectDurationType", source = "projectDurationType", qualifiedByName = "stringToProjectDurationType")
    @Mapping(target = "id", source = "id")
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "revision", ignore = true)
    ProjectElements dtoToElements(ProjectElementsDto dto);

    /**
     * ProjectElementsExtDto转换为ProjectElementsExt
     */
    @Mapping(target = "riskModelChannel", source = "riskModelChannel", qualifiedByName = "stringToRiskModelChannel")
    @Mapping(target = "id", source = "id")
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "revision", ignore = true)
    ProjectElementsExt dtoToElementsExt(ProjectElementsExtDto dto);

    /**
     * 字符串转换为AbleStatus枚举
     */
    @Named("stringToAbleStatus")
    default AbleStatus stringToAbleStatus(String enabled) {
        if (enabled == null || enabled.trim().isEmpty()) {
            return null;
        }
        try {
            return AbleStatus.valueOf(enabled.trim().toUpperCase());
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

    /**
     * 字符串转换为ProjectDurationType枚举
     */
    @Named("stringToProjectDurationType")
    default ProjectDurationType stringToProjectDurationType(String durationType) {
        if (durationType == null || durationType.trim().isEmpty()) {
            return null;
        }
        try {
            return ProjectDurationType.valueOf(durationType.trim().toUpperCase());
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

    /**
     * 字符串转换为RiskModelChannel枚举
     */
    @Named("stringToRiskModelChannel")
    default RiskModelChannel stringToRiskModelChannel(String riskModelChannel) {
        if (riskModelChannel == null || riskModelChannel.trim().isEmpty()) {
            return null;
        }
        try {
            return RiskModelChannel.valueOf(riskModelChannel.trim().toUpperCase());
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

}
